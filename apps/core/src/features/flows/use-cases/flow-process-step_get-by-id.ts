import { Injectable, NotFoundException } from '@nestjs/common';
import { FlowProcessStep, FlowProcessStepService } from '@core/database';

@Injectable()
export class GetFlowProcessStepByIdUseCase implements UseCase {
  constructor(private readonly stepService: FlowProcessStepService) {}

  async execute(id: EntityId): Promise<FlowProcessStep> {
    const flowProcessStep = await this.stepService.findOneBy({ id });

    if (!flowProcessStep)
      throw new NotFoundException('Flow process step not found', 'FLOW_PROCESS_STEP_NOT_FOUND');

    return flowProcessStep;
  }
}
