import { Injectable, NotFoundException } from '@nestjs/common';
import { customAlphabet } from 'nanoid';
import { FlowProcessStep, FlowProcessStepService } from '@core/database';
import { CreateFlowProcessStepDto } from '../dto';

const alphabet = '0123456789abcdefghijklmnopqrstuvwxyz';
const nanoid = customAlphabet(alphabet, 6);

@Injectable()
export class CreateFlowProcessStepUseCase implements UseCase {
  constructor(private readonly stepService: FlowProcessStepService) {}

  async generateMaterializedPath(parentId: EntityId | null): Promise<string> {
    let mPath = '';

    const stepPath = nanoid();

    if (!parentId) {
      mPath = stepPath;
    } else {
      const parent = await this.stepService.findOneBy({ id: parentId });
      if (!parent) throw new NotFoundException('Parent not found', 'PARENT_NOT_FOUND');

      mPath = parent.mPath + '/' + stepPath;
    }

    return mPath;
  }

  /**
   * Potential issue with recursion.
   */
  async recursiveGenerateMaterializedPath(processId: EntityId, parentId: EntityId | null): Promise<string> {
    const mPath = await this.generateMaterializedPath(parentId);

    const existingStepWithSamePath = await this.stepService.findOneBy({ processId, mPath });
    if (existingStepWithSamePath) return await this.recursiveGenerateMaterializedPath(processId, parentId);

    return mPath;
  }

  async execute(processId: EntityId, dto: CreateFlowProcessStepDto): Promise<FlowProcessStep> {
    /** Generate mPath */
    const mPath = await this.recursiveGenerateMaterializedPath(processId, dto.parentId);

    /** Validate prevId */
    const stepWithSamePrevId = await this.stepService.findOneBy({
      processId,
      prevId: dto.prevId,
    });

    /** Create step */
    const step = await this.stepService.createFlowProcessStep({
      stepProps: { ...dto, mPath },
      processId: processId,
    });

    if (stepWithSamePrevId) {
      /** Shift stepWithSamePrevId below */
      await this.stepService.updateFlowProcessStep(stepWithSamePrevId, { prevId: step.id });
    }

    return step;
  }
}
