import { Injectable, NotFoundException } from '@nestjs/common';
import { Flow, FlowService } from '@core/database';

@Injectable()
export class GetFlowByIdUseCase implements UseCase {
  constructor(private readonly flowService: FlowService) {}

  async execute(id: EntityId): Promise<Flow> {
    const flow = await this.flowService.findOneBy({ id });

    if (!flow) throw new NotFoundException('Flow not found', 'FLOW_NOT_FOUND');

    return flow;
  }
}
