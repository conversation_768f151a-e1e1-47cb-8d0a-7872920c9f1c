import { Injectable, NotFoundException } from '@nestjs/common';
import { FlowProcessStep, FlowProcessStepService } from '@core/database';
import { MoveFlowProcessStepDto } from '../dto';

@Injectable()
export class MoveFlowProcessStepUseCase implements UseCase {
  constructor(private readonly stepService: FlowProcessStepService) {}

  async execute(id: EntityId, processId: EntityId, dto: MoveFlowProcessStepDto): Promise<FlowProcessStep> {
    const { parentId: targetParentId, prevId: targetPrevId } = dto;

    // TODO: find many and then use find by result array
    const [step, nextStep, targetStep, targetParentStep] = await Promise.all([
      this.stepService.findOneBy({ processId, id }),
      this.stepService.findOneBy({ processId, prevId: id }),
      this.stepService.findOneBy({ processId, prevId: targetPrevId }),
      targetParentId ? this.stepService.findOneBy({ processId, id: targetParentId }) : null,
    ]);

    if (!step) throw new NotFoundException('Flow process step not found', 'FLOW_PROCESS_STEP_NOT_FOUND');

    const stepPath = step.mPath.split('/').pop();

    /** Shift nextStep above */
    if (nextStep) {
      await this.stepService.updateFlowProcessStep(nextStep, { prevId: step.prevId });
    }

    /** Move step */
    const modifiedStep = await this.stepService.updateFlowProcessStep(step, {
      parentId: targetParentId,
      prevId: targetPrevId,
      mPath: targetParentStep ? targetParentStep.mPath + '/' + stepPath : stepPath,
    });

    /** Shift targetStep below */
    if (targetStep) {
      await this.stepService.updateFlowProcessStep(targetStep, { prevId: modifiedStep.id });
    }

    // TODO: update mPath for children

    return modifiedStep;
  }
}
