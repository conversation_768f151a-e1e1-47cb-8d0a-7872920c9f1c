import { Injectable, NotFoundException } from '@nestjs/common';
import { FlowProcessStep, FlowProcessStepService } from '@core/database';
import { PartialUpdateFlowProcessStepDto, UpdateFlowProcessStepDto } from '../dto';

@Injectable()
export class UpdateFlowProcessStepUseCase implements UseCase {
  constructor(private readonly stepService: FlowProcessStepService) {}

  async execute(
    id: EntityId,
    dto: UpdateFlowProcessStepDto | PartialUpdateFlowProcessStepDto,
  ): Promise<FlowProcessStep> {
    const { name, description } = dto;

    const flowProcessStep = await this.stepService.findOneBy({ id });
    if (!flowProcessStep)
      throw new NotFoundException('Flow process step not found', 'FLOW_PROCESS_STEP_NOT_FOUND');

    return await this.stepService.updateFlowProcessStep(flowProcessStep, { name, description });
  }
}
