import { Body, Controller, Delete, Get, HttpStatus, Param, Patch, Post, Put } from '@nestjs/common';
import { ApiParam } from '@nestjs/swagger';
import { ApiSuccessfulResponse, HttpResponse } from '@libs/common/api';
import {
  CreateFlowProcessStepDto,
  FlowProcessStepDto,
  PartialUpdateFlowProcessStepDto,
  UpdateFlowProcessStepDto,
} from './dto';
import {
  CreateFlowProcessStepUseCase,
  DeleteFlowProcessStepByIdUseCase,
  GetAllFlowProcessStepsUseCase,
  GetFlowProcessStepByIdUseCase,
  UpdateFlowProcessStepUseCase,
} from './use-cases';

const ApiParamFlowProcessStepId = () =>
  ApiParam({ name: 'stepId', required: true, description: 'Flow process step identifier', type: 'string' });

@Controller('flows/:flowId/processes/:processId/steps')
@ApiParam({ name: 'flowId', required: true, description: 'Flow identifier', type: 'string' })
@ApiParam({ name: 'processId', required: true, description: 'Flow process identifier', type: 'string' })
export class FlowProcessStepsController {
  constructor(
    private readonly createFlowProcessStepUseCase: CreateFlowProcessStepUseCase,
    private readonly updateFlowProcessStepUseCase: UpdateFlowProcessStepUseCase,
    private readonly deleteFlowProcessStepByIdUseCase: DeleteFlowProcessStepByIdUseCase,
    private readonly getFlowProcessStepByIdUseCase: GetFlowProcessStepByIdUseCase,
    private readonly getAllFlowProcessStepsUseCase: GetAllFlowProcessStepsUseCase,
  ) {}

  @Post()
  @ApiSuccessfulResponse(HttpStatus.CREATED, 'Flow process step created', FlowProcessStepDto)
  async createFlowProcessStep(
    @Param('flowId') flowId: EntityId,
    @Param('processId') processId: EntityId,
    @Body() dto: CreateFlowProcessStepDto,
  ) {
    const step = await this.createFlowProcessStepUseCase.execute(processId, dto);
    return new HttpResponse({ data: step, message: 'Flow process step created' });
  }

  @Put(':stepId')
  @ApiParamFlowProcessStepId()
  @ApiSuccessfulResponse(HttpStatus.OK, 'Flow process step updated', FlowProcessStepDto)
  async updateFlowProcessStep(
    @Param('flowId') flowId: EntityId,
    @Param('processId') processId: EntityId,
    @Param('stepId') stepId: EntityId,
    @Body() dto: UpdateFlowProcessStepDto,
  ) {
    const step = await this.updateFlowProcessStepUseCase.execute(stepId, dto);
    return new HttpResponse({ data: step, message: 'Flow process step updated' });
  }

  @Patch(':stepId')
  @ApiParamFlowProcessStepId()
  @ApiSuccessfulResponse(HttpStatus.OK, 'Flow process step updated', FlowProcessStepDto)
  async partialUpdateFlowProcessStep(
    @Param('flowId') flowId: EntityId,
    @Param('processId') processId: EntityId,
    @Param('stepId') stepId: EntityId,
    @Body() dto: PartialUpdateFlowProcessStepDto,
  ) {
    await this.updateFlowProcessStepUseCase.execute(stepId, dto);
    return new HttpResponse({ message: 'Flow process step updated' });
  }

  @Delete(':stepId')
  @ApiParamFlowProcessStepId()
  @ApiSuccessfulResponse(HttpStatus.OK, 'Flow process step deleted', FlowProcessStepDto)
  async deleteFlowProcessStep(
    @Param('flowId') flowId: EntityId,
    @Param('processId') processId: EntityId,
    @Param('stepId') stepId: EntityId,
  ) {
    const step = await this.deleteFlowProcessStepByIdUseCase.execute({ processId, stepId });
    return new HttpResponse({ data: step, message: 'Flow process step deleted' });
  }

  @Get(':stepId')
  @ApiParamFlowProcessStepId()
  @ApiSuccessfulResponse(HttpStatus.OK, 'Flow process step found', FlowProcessStepDto)
  async getFlowProcessStep(
    @Param('flowId') flowId: EntityId,
    @Param('processId') processId: EntityId,
    @Param('stepId') stepId: EntityId,
  ) {
    const step = await this.getFlowProcessStepByIdUseCase.execute(stepId);
    return new HttpResponse({ data: step, message: 'Flow process step found' });
  }

  @Get()
  @ApiSuccessfulResponse(HttpStatus.OK, 'Flow process steps found', [FlowProcessStepDto])
  async getFlowProcessSteps(@Param('flowId') flowId: EntityId, @Param('processId') processId: EntityId) {
    const steps = await this.getAllFlowProcessStepsUseCase.execute({ flowProcessId: processId });
    return new HttpResponse({ data: steps, message: 'Flow process steps found' });
  }
}
