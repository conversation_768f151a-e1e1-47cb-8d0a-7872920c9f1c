import { Modu<PERSON> } from '@nestjs/common';
import { DatabaseModule } from '@core/database';
import { FlowProcessStepsController } from './flow-process-steps.controller';
import { FlowProcessesController } from './flow-processes.controller';
import { FlowsController } from './flows.controller';
import {
  GetAllFlowsUseCase,
  CreateFlowUseCase,
  GetFlowByIdUseCase,
  UpdateFlowUseCase,
  DeleteFlowByIdUseCase,
  GetAllFlowProcessesUseCase,
  CreateFlowProcessUseCase,
  GetFlowProcessByIdUseCase,
  UpdateFlowProcessUseCase,
  DeleteFlowProcessByIdUseCase,
  GetAllFlowProcessStepsUseCase,
  CreateFlowProcessStepUseCase,
  GetFlowProcessStepByIdUseCase,
  UpdateFlowProcessStepUseCase,
  DeleteFlowProcessStepByIdUseCase,
} from './use-cases';

@Module({
  imports: [DatabaseModule],
  providers: [
    GetAllFlowsUseCase,
    CreateFlowUseCase,
    GetFlowByIdUseCase,
    UpdateFlowUseCase,
    DeleteFlowByIdUseCase,
    GetAllFlowProcessesUseCase,
    CreateFlowProcessUseCase,
    GetFlowProcessByIdUseCase,
    UpdateFlowProcessUseCase,
    DeleteFlowProcessByIdUseCase,
    GetAllFlowProcessStepsUseCase,
    CreateFlowProcessStepUseCase,
    GetFlowProcessStepByIdUseCase,
    UpdateFlowProcessStepUseCase,
    DeleteFlowProcessStepByIdUseCase,
  ],
  controllers: [FlowsController, FlowProcessesController, FlowProcessStepsController],
})
export class FlowsModule {}
