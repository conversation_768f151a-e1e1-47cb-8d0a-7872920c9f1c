import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

const FlowProcessStepSchema = z.object({
  id: z.string().describe('Flow process step id'),
  name: z.string().describe('Flow process step name'),
  description: z.string().nullable().describe('Flow process step description'),
  parentId: z.string().nullable().describe('Flow process step parent id'),
  prevId: z.string().nullable().describe('Flow process step previous id'),
  mPath: z.string().describe('Flow process step materialized path'),
});

export class FlowProcessStepDto extends createZodDto(FlowProcessStepSchema) {}

const CreateFlowProcessStepSchema = FlowProcessStepSchema.pick({
  name: true,
  description: true,
  parentId: true,
  prevId: true,
});

export class CreateFlowProcessStepDto extends createZodDto(CreateFlowProcessStepSchema) {}

const UpdateFlowProcessStepSchema = FlowProcessStepSchema.pick({
  name: true,
  description: true,
});

export class UpdateFlowProcessStepDto extends createZodDto(UpdateFlowProcessStepSchema) {}

const PartialUpdateFlowProcessStepSchema = UpdateFlowProcessStepSchema.partial();

export class PartialUpdateFlowProcessStepDto extends createZodDto(PartialUpdateFlowProcessStepSchema) {}

const MoveFlowProcessStepSchema = FlowProcessStepSchema.pick({
  parentId: true,
  prevId: true,
});

export class MoveFlowProcessStepDto extends createZodDto(MoveFlowProcessStepSchema) {}
