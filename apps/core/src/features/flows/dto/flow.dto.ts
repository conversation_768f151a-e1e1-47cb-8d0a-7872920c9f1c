import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

const FlowSchema = z.object({
  id: z.string().describe('Flow id'),
  name: z.string().describe('Flow name'),
  description: z.string().nullable().describe('Flow description'),
});

export class FlowDto extends createZodDto(FlowSchema) {}

const CreateFlowSchema = FlowSchema.pick({
  name: true,
  description: true,
});

export class CreateFlowDto extends createZodDto(CreateFlowSchema) {}

const UpdateFlowSchema = FlowSchema.pick({
  name: true,
  description: true,
});

export class UpdateFlowDto extends createZodDto(UpdateFlowSchema) {}

const PartialUpdateFlowSchema = UpdateFlowSchema.partial();

export class PartialUpdateFlowDto extends createZodDto(PartialUpdateFlowSchema) {}
