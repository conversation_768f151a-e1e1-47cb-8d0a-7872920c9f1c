import { Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { APP_FILTER, APP_PIPE } from '@nestjs/core';
import { LoggerModule } from 'nestjs-pino';
import { ZodValidationPipe } from 'nestjs-zod';
import { randomUUID } from 'node:crypto';
import { GlobalApiExceptionFilter } from '@libs/common/api';
import { EnvsModule, includeAppEnvFilePaths } from '@libs/common/envs';
import { EnvironmentVariables, EnvVariablesSchema } from './config';
import { FlowsModule } from './features/flows/flows.module';

@Module({
  imports: [
    LoggerModule.forRootAsync({
      inject: [ConfigService],
      useFactory: (config: ConfigService<EnvironmentVariables>) => ({
        pinoHttp: {
          level: config.get('LOG_LEVEL'),
          genReqId: request => request.headers['x-correlation-id'] || randomUUID(),
          transport: {
            target: 'pino-pretty',
            options: {
              colorize: true,
              singleLine: true,
              ignore: 'pid,context,hostname,req.headers,res',
            },
          },
        },
      }),
    }),

    EnvsModule.forRoot({
      schema: EnvVariablesSchema,
      envFilePath: includeAppEnvFilePaths('core'),
    }),

    FlowsModule,
  ],
  providers: [
    {
      provide: APP_PIPE,
      useClass: ZodValidationPipe,
    },
    {
      provide: APP_FILTER,
      useClass: GlobalApiExceptionFilter,
    },
  ],
})
export class CoreModule {}
