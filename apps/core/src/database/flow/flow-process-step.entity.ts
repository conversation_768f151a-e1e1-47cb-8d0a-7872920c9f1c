import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyTo<PERSON>ne, PrimaryGeneratedColumn } from 'typeorm';
import { FlowProcess } from './flow-process.entity';

@Entity('flow-process-steps')
export class FlowProcessStep {
  @PrimaryGeneratedColumn()
  id: EntityId;

  // TODO
  // @Column({ type: 'varchar' })
  // type: string;

  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string | null;

  @Column({ type: 'integer', nullable: true })
  parentId: EntityId | null;

  @Column({ type: 'integer', nullable: true })
  prevId: EntityId | null;

  @Column({ type: 'varchar' })
  mPath: string;

  @ManyToOne(() => FlowProcess, process => process.steps, {
    nullable: false,
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'processId', referencedColumnName: 'id' })
  flowProcess: FlowProcess;
}
