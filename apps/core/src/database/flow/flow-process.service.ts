import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { FlowProcess } from './flow-process.entity';

type FindOneOptions = {
  id?: EntityId;
};

@Injectable()
export class FlowProcessService {
  constructor(
    @InjectRepository(FlowProcess) private readonly flowProcessRepository: Repository<FlowProcess>,
  ) {}

  private normalizeEntityValues(entity: Partial<FlowProcess>): Partial<FlowProcess> {
    const flowProcess = new FlowProcess();
    Object.assign(flowProcess, entity);
    if (flowProcess.description === '') flowProcess.description = null;
    return flowProcess;
  }

  async findOneBy(options: FindOneOptions): Promise<FlowProcess | null> {
    return await this.flowProcessRepository.findOneBy(options);
  }

  async findAll({ flowId }: { flowId: EntityId }): Promise<FlowProcess[]> {
    return await this.flowProcessRepository.find({
      where: {
        flow: { id: flowId },
      },
    });
  }

  async findMaxFlowProcessIndex(): Promise<number> {
    const result = await this.flowProcessRepository
      .createQueryBuilder('entity')
      .select('MAX(entity.index)')
      .getRawOne<{ max: number }>();

    return result?.max ?? 0;
  }

  async createFlowProcess(data: {
    flowProcess: Pick<FlowProcess, 'name' | 'description' | 'index' | 'parentId'>;
    flowId: EntityId;
  }): Promise<FlowProcess> {
    const flowProcess = this.flowProcessRepository.create({
      ...this.normalizeEntityValues(data.flowProcess),
      flow: { id: data.flowId },
    });
    return await this.flowProcessRepository.save(flowProcess);
  }

  async updateFlowProcess(flowProcess: FlowProcess, data: Partial<FlowProcess>): Promise<FlowProcess> {
    const result = await this.flowProcessRepository.update(flowProcess.id, this.normalizeEntityValues(data));
    return result.affected ? Object.assign(flowProcess, data) : flowProcess;
  }

  async deleteFlowProcess(flowProcess: FlowProcess): Promise<FlowProcess> {
    await this.flowProcessRepository.delete(flowProcess.id);
    return flowProcess;
  }
}
