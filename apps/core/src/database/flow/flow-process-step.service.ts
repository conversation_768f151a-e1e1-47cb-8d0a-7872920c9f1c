import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Like, Repository } from 'typeorm';
import { FlowProcessStep } from './flow-process-step.entity';

type FindOneOptions = {
  id?: EntityId;
  mPath?: string;
  processId?: EntityId;
  prevId?: EntityId | null;
};

@Injectable()
export class FlowProcessStepService {
  constructor(
    @InjectRepository(FlowProcessStep)
    private readonly flowProcessStepRepository: Repository<FlowProcessStep>,
  ) {}

  private normalizeEntityValues(entity: Partial<FlowProcessStep>): Partial<FlowProcessStep> {
    const flowProcessStep = new FlowProcessStep();
    Object.assign(flowProcessStep, entity);
    if (flowProcessStep.description === '') flowProcessStep.description = null;
    return flowProcessStep;
  }

  async findOneBy(options: FindOneOptions): Promise<FlowProcessStep | null> {
    const conditions: Record<string, unknown> = { ...options };

    if (conditions.processId) {
      conditions.flowProcess = { id: conditions.processId };
      delete conditions.processId;
    }

    return await this.flowProcessStepRepository.createQueryBuilder().where(conditions).getOne();
  }

  async findAll({ processId }: { processId: EntityId }): Promise<FlowProcessStep[]> {
    return await this.flowProcessStepRepository.find({
      where: {
        flowProcess: { id: processId },
      },
    });
  }

  async findChildrenByMPath({
    processId,
    mPath,
  }: {
    processId: EntityId;
    mPath: string;
  }): Promise<FlowProcessStep[]> {
    return await this.flowProcessStepRepository.find({
      where: {
        flowProcess: { id: processId },
        mPath: Like(`${mPath}/%`),
      },
    });
  }

  async deleteChildrenByMPath({ processId, mPath }: { processId: EntityId; mPath: string }): Promise<void> {
    await this.flowProcessStepRepository.delete({
      flowProcess: { id: processId },
      mPath: Like(`${mPath}/%`),
    });
  }

  async createFlowProcessStep(data: {
    stepProps: Pick<FlowProcessStep, 'name' | 'description' | 'parentId' | 'prevId' | 'mPath'>;
    processId: EntityId;
  }): Promise<FlowProcessStep> {
    const flowProcessStep = this.flowProcessStepRepository.create({
      ...this.normalizeEntityValues(data.stepProps),
      flowProcess: { id: data.processId },
    });
    return await this.flowProcessStepRepository.save(flowProcessStep);
  }

  async updateFlowProcessStep(
    flowProcessStep: FlowProcessStep,
    data: Partial<FlowProcessStep>,
  ): Promise<FlowProcessStep> {
    const result = await this.flowProcessStepRepository.update(
      flowProcessStep.id,
      this.normalizeEntityValues(data),
    );
    return result.affected ? Object.assign(flowProcessStep, data) : flowProcessStep;
  }

  async deleteFlowProcessStep(flowProcessStep: FlowProcessStep): Promise<FlowProcessStep> {
    await this.flowProcessStepRepository.delete(flowProcessStep.id);
    return flowProcessStep;
  }

  //
}
